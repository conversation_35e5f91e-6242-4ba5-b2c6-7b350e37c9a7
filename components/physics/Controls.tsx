import { Dispatch, SetStateAction } from 'react';
import { ModeToggle } from '@/components/mode-toggle';

import { TextOptions, DrawingTool, SpawnerConfig, ActivePreset } from './types';
import { presets } from './presets';

interface ControlsProps {
  inputText: string;
  setInputText: (value: string) => void;
  renderText: () => void;
  dropText: () => void;
  isTextRendered: boolean;
  isCharacterMode: boolean;
  setIsCharacterMode: Dispatch<SetStateAction<boolean>>;
  isAltMode: boolean;
  setIsAltMode: Dispatch<SetStateAction<boolean>>;
  reset: () => void;
  gravity: number;
  setGravity: Dispatch<SetStateAction<number>>;
  friction: number;
  setFriction: Dispatch<SetStateAction<number>>;
  restitution: number;
  setRestitution: Dispatch<SetStateAction<number>>;
  isPaused: boolean;
  setIsPaused: Dispatch<SetStateAction<boolean>>;
  textOptions: TextOptions;
  onSelectPreset: (presetName: string) => void;
  canvasColor: string;
  onCanvasColorChange: (color: string) => void;
  collisionColor: string;
  onCollisionColorChange: (color: string) => void;
  floorColor: string;
  onFloorColorChange: (color: string) => void;
  wallOffsetX: number;
  setWallOffsetX: (value: number) => void;
  wallOffsetY: number;
  setWallOffsetY: (value: number) => void;
  wallBorderColor: string;
  onWallBorderColorChange: (color: string) => void;
  drawingTool: DrawingTool;
  setDrawingTool: (tool: DrawingTool) => void;
  penSize: number;
  setPenSize: (size: number) => void;
  penColor: string;
  setPenColor: (color: string) => void;
  isLowPoly: boolean;
  setIsLowPoly: (value: boolean) => void;
  showInvisible: boolean;
  setShowInvisible: (value: boolean) => void;
  onSaveDrawing: () => void;
  onLoadDrawing: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onDeleteAllPresets: () => void;
  onAdd10Random: () => void;
  activePresetsCount: number;
  performanceOptimizations: {
    suspendStaticBodies: boolean;
    disableCollisionDetection: boolean;
    reduceRenderFrequency: boolean;
    freezeDistantBodies: boolean;
    batchPhysicsUpdates: boolean;
  };
  onPerformanceToggle: (optimization: keyof {
    suspendStaticBodies: boolean;
    disableCollisionDetection: boolean;
    reduceRenderFrequency: boolean;
    freezeDistantBodies: boolean;
    batchPhysicsUpdates: boolean;
  }) => void;
  shouldSuggestOptimizations: boolean;
  isHighBodyCount: boolean;
  isTooManyBodies?: boolean;
  // Spawner-related props
  spawnerConfig: SpawnerConfig;
  onSpawnerConfigChange: (config: Partial<SpawnerConfig>) => void;
  activePresets: ActivePreset[];
}

const Controls: React.FC<ControlsProps> = ({
  inputText, setInputText, renderText, dropText, isTextRendered, isCharacterMode, setIsCharacterMode, isAltMode, setIsAltMode, reset, gravity, setGravity, friction, setFriction, restitution, setRestitution, isPaused, setIsPaused, textOptions, onSelectPreset, canvasColor, onCanvasColorChange, collisionColor, onCollisionColorChange, floorColor, onFloorColorChange,
  wallOffsetX,
  setWallOffsetX,
  wallOffsetY,
  setWallOffsetY,
  wallBorderColor,
  onWallBorderColorChange,
  drawingTool,
  setDrawingTool,
  penSize,
  setPenSize,
  penColor,
  setPenColor,
  isLowPoly,
  setIsLowPoly,
  showInvisible,
  setShowInvisible,
  onSaveDrawing,
  onLoadDrawing,
  onDeleteAllPresets,
  onAdd10Random,
  activePresetsCount,
  performanceOptimizations,
  onPerformanceToggle,
  shouldSuggestOptimizations,
  isHighBodyCount,
  isTooManyBodies = false,
  spawnerConfig,
  onSpawnerConfigChange,
  activePresets
}) => {
  return (
    <div className="bg-secondary p-4 w-full">
      <div className="flex flex-wrap justify-around items-start gap-6 max-w-7xl mx-auto">

        {/* Column 1: Text & Actions */}
        <div className="flex flex-col gap-4 p-4 bg-card rounded-lg shadow-md min-w-[300px]">
          <div className="flex justify-between items-center border-b pb-2">
            <h3 className="text-xl font-semibold">Text & Actions</h3>
            <ModeToggle />
          </div>
          
          {/* Preset Selector */}
          <div>
            <label className="block text-sm font-medium text-muted-foreground mb-1">Load Preset</label>
            <select
              onChange={(e) => onSelectPreset(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              defaultValue=""
            >
              <option value="" disabled>Select a Preset</option>
              {presets.map(p => (
                <option key={p.name} value={p.name}>{p.name}</option>
              ))}
            </select>
          </div>

          {/* Text Input */}
          <div>
            <label className="block text-sm font-medium text-muted-foreground mb-1">Add Text</label>
            <input
              type="text"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && renderText()}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter text..."
            />
            <div className="flex gap-2 mt-2">
              <button onClick={renderText} className="flex-1 bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600 transition-colors">Render</button>
              <button onClick={dropText} disabled={!isTextRendered} className="flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 transition-colors disabled:bg-gray-300">Drop</button>
            </div>
          </div>

          {/* Mode Toggle */}
          <div className="flex gap-4">
            <label className="flex items-center space-x-3 cursor-pointer">
              <input type="checkbox" checked={isCharacterMode} onChange={(e) => setIsCharacterMode(e.target.checked)} className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500" />
              <span className="text-sm font-medium text-muted-foreground">Character Mode</span>
            </label>
            <label className="flex items-center space-x-3 cursor-pointer">
              <input type="checkbox" checked={isAltMode} onChange={(e) => setIsAltMode(e.target.checked)} className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500" />
              <span className="text-sm font-medium text-muted-foreground">Alt Mode</span>
            </label>
          </div>

          {/* Simulation Controls */}
          <div className="flex gap-2">
            <button onClick={() => setIsPaused(!isPaused)} className={`flex-1 text-white py-2 px-4 rounded-md transition-colors ${isPaused ? 'bg-green-500 hover:bg-green-600' : 'bg-yellow-500 hover:bg-yellow-600'}`}>
              {isPaused ? 'Play' : 'Pause'}
            </button>
            <button onClick={reset} className="flex-1 bg-red-500 text-white py-2 px-4 rounded-md hover:bg-red-600 transition-colors">Reset</button>
          </div>

          {/* Memory Management */}
          <div className="flex gap-2">
            <button
              onClick={onDeleteAllPresets}
              disabled={activePresetsCount === 0}
              className={`flex-1 py-2 px-4 rounded-md transition-colors ${
                activePresetsCount === 0
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-orange-500 text-white hover:bg-orange-600'
              }`}
              title={activePresetsCount === 0 ? 'No presets to delete' : `Delete all ${activePresetsCount} active presets and free memory`}
            >
              🗑️ Clear All ({activePresetsCount})
            </button>
            <button
              onClick={onAdd10Random}
              className="flex-1 py-2 px-4 rounded-md bg-purple-500 text-white hover:bg-purple-600 transition-colors"
              title="Add 10 random text elements with different colors and sizes"
            >
              🎲 Add 10 Random
            </button>
          </div>
        </div>

        {/* Column 2: Physics Parameters */}
        <div className="flex flex-col gap-4 p-4 bg-card rounded-lg shadow-md min-w-[300px]">
          <h3 className="text-xl font-semibold border-b pb-2">Physics</h3>
          <div>
            <label className="block text-sm font-medium text-muted-foreground mb-1">Gravity: {gravity.toFixed(2)}</label>
            <input type="range" min="0" max="2" step="0.1" value={gravity} onChange={(e) => setGravity(parseFloat(e.target.value))} className="w-full" />
          </div>
          <div>
            <label className="block text-sm font-medium text-muted-foreground mb-1">Friction: {friction.toFixed(2)}</label>
            <input type="range" min="0" max="1" step="0.05" value={friction} onChange={(e) => setFriction(parseFloat(e.target.value))} className="w-full" />
          </div>
          <div>
            <label className="block text-sm font-medium text-muted-foreground mb-1">Restitution: {restitution.toFixed(2)}</label>
            <input type="range" min="0" max="1" step="0.05" value={restitution} onChange={(e) => setRestitution(parseFloat(e.target.value))} className="w-full" />
          </div>
        </div>

        {/* Column 3: Scene Colors */}
        <div className="flex flex-col gap-4 p-4 bg-card rounded-lg shadow-md min-w-[300px]">
          <h3 className="text-xl font-semibold border-b pb-2">Colors</h3>
          <div className="flex justify-between items-center">
            <label className="text-sm font-medium text-muted-foreground">Canvas</label>
            <input type="color" value={canvasColor} onChange={(e) => onCanvasColorChange(e.target.value)} className="w-16 h-8 border-none rounded-md cursor-pointer" />
          </div>
          <div className="flex justify-between items-center">
            <label className="text-sm font-medium text-muted-foreground">Collisions</label>
            <input type="color" value={collisionColor} onChange={(e) => onCollisionColorChange(e.target.value)} className="w-16 h-8 border-none rounded-md cursor-pointer" />
          </div>
          <div className="flex justify-between items-center">
            <label className="text-sm font-medium text-muted-foreground">Floor</label>
            <input type="color" value={floorColor} onChange={(e) => onFloorColorChange(e.target.value)} className="w-16 h-8 border-none rounded-md cursor-pointer" />
          </div>
        </div>

        {/* Column 4: Debugging */}
        <div className="flex flex-col gap-4 p-4 bg-card rounded-lg shadow-md min-w-[300px]">
          <h3 className="text-xl font-semibold border-b pb-2">Debug</h3>
          <div>
            <label className="block text-sm font-medium text-muted-foreground mb-1">Wall X Offset: {wallOffsetX.toFixed(0)}</label>
            <input type="range" min="-200" max="200" step="1" value={wallOffsetX} onChange={(e) => setWallOffsetX(parseFloat(e.target.value))} className="w-full" />
          </div>
          <div>
            <label className="block text-sm font-medium text-muted-foreground mb-1">Wall Y Offset: {wallOffsetY.toFixed(0)}</label>
            <input type="range" min="-200" max="200" step="1" value={wallOffsetY} onChange={(e) => setWallOffsetY(parseFloat(e.target.value))} className="w-full" />
          </div>
          <div className="flex justify-between items-center">
            <label className="text-sm font-medium text-muted-foreground">Wall Border</label>
            <input type="color" value={wallBorderColor} onChange={(e) => onWallBorderColorChange(e.target.value)} className="w-16 h-8 border-none rounded-md cursor-pointer" />
          </div>
        </div>

        {/* Column 5: Drawing */}
        <div className="flex flex-col gap-4 p-4 bg-card rounded-lg shadow-md min-w-[300px]">
          <h3 className="text-xl font-semibold border-b pb-2">Drawing</h3>
          <div className="flex gap-2">
            <button onClick={onSaveDrawing} className="flex-1 bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 transition-colors">Save</button>
            <label className="flex-1 bg-purple-500 text-white py-2 px-4 rounded-md hover:bg-purple-600 transition-colors cursor-pointer text-center">
              Load
              <input type="file" accept=".json" onChange={onLoadDrawing} className="hidden" />
            </label>
          </div>
          <div className="flex items-center gap-2 flex-wrap">
            <button onClick={() => setDrawingTool('hand')} className={`px-3 py-1 rounded ${drawingTool === 'hand' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}>Hand</button>
            <button onClick={() => setDrawingTool('pen')} className={`px-3 py-1 rounded ${drawingTool === 'pen' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}>Pen</button>
            <button onClick={() => setDrawingTool('eraser')} className={`px-3 py-1 rounded ${drawingTool === 'eraser' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}>Eraser</button>
            <button onClick={() => setDrawingTool('spawner')} className={`px-3 py-1 rounded ${drawingTool === 'spawner' ? 'bg-green-500 text-white' : 'bg-gray-200'}`}>Spawner</button>
            <button onClick={() => setPenColor('transparent')} className={`px-3 py-1 rounded ${penColor === 'transparent' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}>Transparent</button>
          </div>
          <div>
            <label className="block text-sm font-medium text-muted-foreground mb-1">Pen Size: {penSize}</label>
            <input type="range" min="1" max="50" step="1" value={penSize} onChange={(e) => setPenSize(parseInt(e.target.value))} className="w-full" />
          </div>
          <div className="flex justify-between items-center">
            <label className="text-sm font-medium text-muted-foreground">Pen Color</label>
            <input type="color" value={penColor} onChange={(e) => setPenColor(e.target.value)} className="w-16 h-8 border-none rounded-md cursor-pointer" />
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="low-poly-checkbox"
              checked={isLowPoly}
              onChange={(e) => setIsLowPoly(e.target.checked)}
              className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
            />
            <label htmlFor="low-poly-checkbox" className="ml-2 text-sm font-medium text-muted-foreground">Low Poly</label>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="show-invisible-checkbox"
              checked={showInvisible}
              onChange={(e) => setShowInvisible(e.target.checked)}
              className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
            />
            <label htmlFor="show-invisible-checkbox" className="ml-2 text-sm font-medium text-muted-foreground">Show Invisible</label>
          </div>

          {/* Spawner Configuration Panel - Only show when spawner tool is selected */}
          {drawingTool === 'spawner' && (
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="text-lg font-medium text-green-800 mb-3">Spawner Configuration</h4>

              {/* Preset Selection */}
              <div className="mb-3">
                <label className="block text-sm font-medium text-green-700 mb-1">Preset to Spawn</label>
                <select
                  value={spawnerConfig.selectedPresetId || ''}
                  onChange={(e) => onSpawnerConfigChange({ selectedPresetId: e.target.value ? parseInt(e.target.value) : null })}
                  className="w-full px-3 py-2 border border-green-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="">Select a preset...</option>
                  {activePresets.map(preset => (
                    <option key={preset.id} value={preset.id}>{preset.name}</option>
                  ))}
                </select>
              </div>

              {/* Spawn Interval */}
              <div className="mb-3">
                <label className="block text-sm font-medium text-green-700 mb-1">
                  Spawn Interval: {spawnerConfig.spawnInterval.toFixed(1)}s
                </label>
                <input
                  type="range"
                  min="0.1"
                  max="10"
                  step="0.1"
                  value={spawnerConfig.spawnInterval}
                  onChange={(e) => onSpawnerConfigChange({ spawnInterval: parseFloat(e.target.value) })}
                  className="w-full"
                  disabled={spawnerConfig.spawnOnce}
                />
              </div>

              {/* Spawn Once Toggle */}
              <div className="mb-3 flex items-center">
                <input
                  type="checkbox"
                  id="spawn-once"
                  checked={spawnerConfig.spawnOnce}
                  onChange={(e) => onSpawnerConfigChange({ spawnOnce: e.target.checked })}
                  className="w-4 h-4 text-green-600 rounded focus:ring-green-500"
                />
                <label htmlFor="spawn-once" className="ml-2 text-sm font-medium text-green-700">Spawn Once (Single spawn)</label>
              </div>

              {/* Spawn Count Limit */}
              <div className="mb-3">
                <div className="flex items-center mb-1">
                  <input
                    type="checkbox"
                    id="enable-spawn-limit"
                    checked={spawnerConfig.spawnCountLimit !== null}
                    onChange={(e) => onSpawnerConfigChange({
                      spawnCountLimit: e.target.checked ? 10 : null
                    })}
                    className="w-4 h-4 text-green-600 rounded focus:ring-green-500"
                  />
                  <label htmlFor="enable-spawn-limit" className="ml-2 text-sm font-medium text-green-700">
                    Limit Total Spawns: {spawnerConfig.spawnCountLimit || 'Unlimited'}
                  </label>
                </div>
                {spawnerConfig.spawnCountLimit !== null && (
                  <input
                    type="range"
                    min="1"
                    max="50"
                    step="1"
                    value={spawnerConfig.spawnCountLimit}
                    onChange={(e) => onSpawnerConfigChange({ spawnCountLimit: parseInt(e.target.value) })}
                    className="w-full"
                  />
                )}
              </div>

              {/* Initial Velocity */}
              <div className="mb-3">
                <label className="block text-sm font-medium text-green-700 mb-1">Initial Velocity</label>
                <div className="flex gap-2">
                  <div className="flex-1">
                    <label className="block text-xs text-green-600 mb-1">X: {spawnerConfig.initialVelocity.x.toFixed(1)}</label>
                    <input
                      type="range"
                      min="-10"
                      max="10"
                      step="0.1"
                      value={spawnerConfig.initialVelocity.x}
                      onChange={(e) => onSpawnerConfigChange({
                        initialVelocity: { ...spawnerConfig.initialVelocity, x: parseFloat(e.target.value) }
                      })}
                      className="w-full"
                    />
                  </div>
                  <div className="flex-1">
                    <label className="block text-xs text-green-600 mb-1">Y: {spawnerConfig.initialVelocity.y.toFixed(1)}</label>
                    <input
                      type="range"
                      min="-10"
                      max="10"
                      step="0.1"
                      value={spawnerConfig.initialVelocity.y}
                      onChange={(e) => onSpawnerConfigChange({
                        initialVelocity: { ...spawnerConfig.initialVelocity, y: parseFloat(e.target.value) }
                      })}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>

              {!spawnerConfig.selectedPresetId && (
                <div className="text-sm text-green-600 bg-green-100 p-2 rounded">
                  💡 Select a preset above, then click on the canvas to place spawners!
                </div>
              )}
            </div>
          )}
        </div>

        {/* Column 6: Performance Optimizations */}
        <div className="flex flex-col gap-4 p-4 bg-card rounded-lg shadow-md min-w-[350px]">
          <div className="flex justify-between items-center border-b pb-2">
            <h3 className="text-xl font-semibold">Performance Optimizations</h3>
            <div className="flex gap-2 items-center">
              {shouldSuggestOptimizations && (
                <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                  Suggested ({activePresetsCount} bodies)
                </span>
              )}

            </div>
          </div>

          {/* Auto-enable suggestion */}
          {isTooManyBodies && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-center gap-2">
                <span className="text-red-600">🚫</span>
                <span className="text-sm text-red-700 font-medium">
                  Too many bodies ({activePresetsCount})! Optimizations disabled to prevent performance issues. Consider clearing some elements.
                </span>
              </div>
            </div>
          )}

          {shouldSuggestOptimizations && !isTooManyBodies && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 text-sm">
              <p className="text-yellow-800 font-medium">⚡ Performance Impact Detected</p>
              <p className="text-yellow-700 mt-1">
                With {activePresetsCount} bodies, consider enabling optimizations below for better performance.
              </p>
            </div>
          )}

          {/* Optimization Toggles */}
          <div className={`space-y-3 ${isTooManyBodies ? 'opacity-50 pointer-events-none' : ''}`}>
            {/* Suspend Static Bodies */}
            <div className="flex items-start gap-3">
              <input
                type="checkbox"
                id="suspend-static-bodies"
                checked={performanceOptimizations.suspendStaticBodies}
                onChange={() => onPerformanceToggle('suspendStaticBodies')}
                className="w-5 h-5 text-blue-600 rounded focus:ring-blue-500 mt-1 border-2 border-gray-400"
              />
              <div className="flex-1">
                <label htmlFor="suspend-static-bodies" className="text-sm font-medium text-foreground cursor-pointer">
                  Suspend Physics for Static Bodies
                </label>
                <p className="text-xs text-muted-foreground mt-1">
                  Disables physics for bodies that haven't moved in 3+ seconds.
                  <span className="text-green-600 font-medium"> ~30% CPU reduction</span>
                </p>
                <p className="text-xs text-blue-600">Auto-enable when &gt;20 bodies</p>
              </div>
            </div>

            {/* Disable Collision Detection */}
            <div className="flex items-start gap-3">
              <input
                type="checkbox"
                id="disable-collision-detection"
                checked={performanceOptimizations.disableCollisionDetection}
                onChange={() => onPerformanceToggle('disableCollisionDetection')}
                className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500 mt-1"
              />
              <div className="flex-1">
                <label htmlFor="disable-collision-detection" className="text-sm font-medium text-foreground cursor-pointer">
                  Disable Text-to-Text Collisions
                </label>
                <p className="text-xs text-muted-foreground mt-1">
                  Keeps wall/floor collisions but disables text body interactions.
                  <span className="text-green-600 font-medium"> ~40% collision reduction</span>
                </p>
                <p className="text-xs text-blue-600">Auto-enable when &gt;25 bodies</p>
              </div>
            </div>

            {/* Reduce Render Frequency */}
            <div className="flex items-start gap-3">
              <input
                type="checkbox"
                id="reduce-render-frequency"
                checked={performanceOptimizations.reduceRenderFrequency}
                onChange={() => onPerformanceToggle('reduceRenderFrequency')}
                className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500 mt-1"
              />
              <div className="flex-1">
                <label htmlFor="reduce-render-frequency" className="text-sm font-medium text-foreground cursor-pointer">
                  Reduce Text Render Frequency
                </label>
                <p className="text-xs text-muted-foreground mt-1">
                  Renders text every 2-3 frames instead of every frame.
                  <span className="text-green-600 font-medium"> ~50% render reduction</span>
                </p>
                <p className="text-xs text-blue-600">Auto-enable when &gt;15 bodies</p>
              </div>
            </div>

            {/* Freeze Distant Bodies */}
            <div className="flex items-start gap-3">
              <input
                type="checkbox"
                id="freeze-distant-bodies"
                checked={performanceOptimizations.freezeDistantBodies}
                onChange={() => onPerformanceToggle('freezeDistantBodies')}
                className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500 mt-1"
              />
              <div className="flex-1">
                <label htmlFor="freeze-distant-bodies" className="text-sm font-medium text-foreground cursor-pointer">
                  Freeze Off-Screen Bodies
                </label>
                <p className="text-xs text-muted-foreground mt-1">
                  Suspends physics for bodies far from viewport or below screen.
                  <span className="text-green-600 font-medium"> ~25% physics reduction</span>
                </p>
                <p className="text-xs text-blue-600">Auto-enable when &gt;30 bodies</p>
              </div>
            </div>

            {/* Batch Physics Updates */}
            <div className="flex items-start gap-3">
              <input
                type="checkbox"
                id="batch-physics-updates"
                checked={performanceOptimizations.batchPhysicsUpdates}
                onChange={() => onPerformanceToggle('batchPhysicsUpdates')}
                className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500 mt-1"
              />
              <div className="flex-1">
                <label htmlFor="batch-physics-updates" className="text-sm font-medium text-foreground cursor-pointer">
                  Batch Physics Processing
                </label>
                <p className="text-xs text-muted-foreground mt-1">
                  Processes physics updates in smaller batches per frame.
                  <span className="text-green-600 font-medium"> ~20% smoother frames</span>
                </p>
                <p className="text-xs text-blue-600">Auto-enable when &gt;35 bodies</p>
              </div>
            </div>
          </div>

          {/* Performance Impact Summary */}
          {Object.values(performanceOptimizations).some(Boolean) && (
            <div className="bg-green-50 border border-green-200 rounded-md p-3 text-sm">
              <p className="text-green-800 font-medium">🚀 Active Optimizations</p>
              <p className="text-green-700 mt-1">
                {Object.values(performanceOptimizations).filter(Boolean).length} optimization(s) enabled.
                Monitor FPS in performance panel to see improvements.
              </p>
            </div>
          )}
        </div>

      </div>
    </div>
  );
};

export default Controls;
